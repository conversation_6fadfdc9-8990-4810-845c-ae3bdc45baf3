import {LOGGER, onFirebaseRequest} from '../../../firebase';
import {getAppUserById} from '../../../firestore';
import {withAuthenticatedUser} from '../../../firestoreHelpers';
import {StatusCodes, type UUIDRequestDTO} from '../../../types';
import {
  composeMiddleware,
  getClientAppUserDTO2,
  isDefinedString,
  ResponseCacheDuration,
  withResponseCache,
  withValidPostRequest,
} from '../../../utils';

export const clientSummary2 = onFirebaseRequest(
  {memory: '512MiB'},
  composeMiddleware(
    'clientSummary2',
    async (request, response) => {
      const {id} = request.body as Partial<UUIDRequestDTO>;

      if (!isDefinedString(id)) {
        LOGGER.warn('Missing client id', request);
        response.status(StatusCodes.BAD_REQUEST_400).send('Missing client id').end();
        return;
      }
      const appUser = await getAppUserById(id);
      const doesExist = !!appUser;

      if (!doesExist) {
        LOGGER.warn('User not found with that ID', request);
        response.status(StatusCodes.BAD_REQUEST_400).send('User not found with that ID').end();
        return;
      }

      const startTimeMs2 = Date.now();
      const clientAppUser = await getClientAppUserDTO2(appUser);
      const endTimeMs2 = Date.now();
      LOGGER.debug(`Calculated summary for client ${id} in ${(endTimeMs2 - startTimeMs2) / 1000}s`);

      LOGGER.debug(`Client with id ${appUser.id} responded with`, clientAppUser);

      response.status(StatusCodes.OK_200).send(clientAppUser).end();
    },
    withValidPostRequest,
    withResponseCache(ResponseCacheDuration.ONE_HOUR),
    withAuthenticatedUser('9624d39a-65e9-4e33-810f-48aabceb8f2d'),
  ),
);
