import {DOMAIN_CONSTANTS, type IsoDate} from '../../types';
import {calculateStreak} from './appUserTransformer';

// Mock the LOGGER to avoid console output during tests
jest.mock('../../firebase', () => ({
  LOGGER: {
    debug: jest.fn(),
  },
}));

describe('calculateStreak', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when streakStart or streakEnd is undefined', () => {
    it('should return 0 days when streakStart is undefined', () => {
      // Arrange
      const streakStart = undefined;
      const streakEnd = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({days: 0});
    });

    it('should return 0 days when streakEnd is undefined', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = undefined;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({days: 0});
    });

    it('should return 0 days when both streakStart and streakEnd are undefined', () => {
      // Arrange
      const streakStart = undefined;
      const streakEnd = undefined;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({days: 0});
    });
  });

  describe('when todayIsoDate is provided', () => {
    it('should return streak when streakEnd is today', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 5, // 2025-06-25 to 2025-06-29 inclusive = 5 days
      });
    });

    it('should return streak when streakEnd is yesterday', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-28' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 4, // 2025-06-25 to 2025-06-28 inclusive = 4 days
      });
    });

    it('should return 0 days when streakEnd is neither today nor yesterday', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-27' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({days: 0});
    });
  });

  describe('when streak length is below minimum threshold', () => {
    it('should return 0 days when streak is shorter than minimum length', () => {
      // Arrange
      const minLength = DOMAIN_CONSTANTS().STREAKS.MIN_MOVEMENT_STREAK_LENGTH;
      // Create a streak that's 1 day shorter than minimum (if min is 1, this won't apply)
      // Since we know from the codebase that MIN_MOVEMENT_STREAK_LENGTH is 1,
      // we need to test with a 0-day streak (same start and end date, non-inclusive)
      const streakStart = '2025-06-29' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      // With inclusive calculation, same date = 1 day, which meets minimum of 1
      // So this should actually return the streak
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 1,
      });
    });
  });

  describe('test cases from user data', () => {
    it('should calculate current streak correctly', () => {
      // Arrange - Current streak data from user
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate; // Assuming today is 2025-06-29

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 5, // 2025-06-25 to 2025-06-29 inclusive = 5 days
      });
    });

    it('should calculate longest streak correctly', () => {
      // Arrange - Longest streak data from user (no todayIsoDate check)
      const streakStart = '2023-11-11' as IsoDate;
      const streakEnd = '2023-10-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      // Note: This appears to be invalid data (start date after end date)
      // getIsoDateDifference('2023-10-29', '2023-11-11', true) should be negative
      // Let's calculate: Oct 29 to Nov 11 = -13 days (end before start)
      // With inclusive: -13 - 1 = -14 days
      expect(result).toEqual({days: 0}); // Should be 0 due to minimum length check
    });

    it('should handle valid longest streak data', () => {
      // Arrange - Corrected longest streak data (assuming the dates were swapped)
      const streakStart = '2023-10-29' as IsoDate;
      const streakEnd = '2023-11-11' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      // Oct 29 to Nov 11 inclusive = 14 days
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 14,
      });
    });
  });

  describe('edge cases', () => {
    it('should handle single day streak', () => {
      // Arrange
      const streakStart = '2025-06-29' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 1,
      });
    });

    it('should handle streak that ended exactly yesterday', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-28' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 4, // 2025-06-25 to 2025-06-28 inclusive = 4 days
      });
    });

    it('should handle streak that ended two days ago', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-27' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({days: 0});
    });
  });

  describe('without todayIsoDate parameter', () => {
    it('should calculate streak without today validation', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 5,
      });
    });

    it('should calculate historical streak without today validation', () => {
      // Arrange
      const streakStart = '2023-01-01' as IsoDate;
      const streakEnd = '2023-01-10' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 10, // Jan 1 to Jan 10 inclusive = 10 days
      });
    });
  });
});
