import {type IsoDate} from '../../types';
import {calculateStreak} from './streakCalculator';

describe('calculateStreak', () => {
  describe('when streakStart or streakEnd is undefined', () => {
    it('should return 0 days when streakStart is undefined', () => {
      // Arrange
      const streakStart = undefined;
      const streakEnd = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({days: 0});
    });

    it('should return 0 days when streakEnd is undefined', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = undefined;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({days: 0});
    });

    it('should return 0 days when both streakStart and streakEnd are undefined', () => {
      // Arrange
      const streakStart = undefined;
      const streakEnd = undefined;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({days: 0});
    });
  });

  describe('when todayIsoDate is provided', () => {
    it('should return streak when streakEnd is today', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 5, // 2025-06-25 to 2025-06-29 inclusive = 5 days
      });
    });

    it('should return streak when streakEnd is yesterday', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-28' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 4, // 2025-06-25 to 2025-06-28 inclusive = 4 days
      });
    });

    it('should return 0 days when streakEnd is neither today nor yesterday', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-27' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({days: 0});
    });
  });

  describe('when streak length is below minimum threshold', () => {
    it('should return 0 days when streak is shorter than minimum length', () => {
      // Arrange
      // Create a streak that's 1 day shorter than minimum (if min is 1, this won't apply)
      // Since we know from the codebase that MIN_MOVEMENT_STREAK_LENGTH is 1,
      // we need to test with a 0-day streak (same start and end date, non-inclusive)
      const streakStart = '2025-06-29' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      // With inclusive calculation, same date = 1 day, which meets minimum of 1
      // So this should actually return the streak
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 1,
      });
    });
  });

  describe('test cases from user data', () => {
    it('should calculate current streak correctly', () => {
      // Arrange - Current streak data from user
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate; // Assuming today is 2025-06-29

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 5, // 2025-06-25 to 2025-06-29 inclusive = 5 days
      });
    });

    it('should calculate longest streak correctly with flipped dates', () => {
      // Arrange - Longest streak data from user (dates are flipped)
      const streakStart = '2023-11-11' as IsoDate; // This is actually the end date
      const streakEnd = '2023-10-29' as IsoDate; // This is actually the start date

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      // Function should automatically detect and correct the date order
      // Oct 29 to Nov 11 inclusive = 14 days
      expect(result).toEqual({
        dates: ['2023-10-29' as IsoDate, '2023-11-11' as IsoDate], // Corrected order
        days: 14,
      });
    });

    it('should handle valid longest streak data', () => {
      // Arrange - Corrected longest streak data (assuming the dates were swapped)
      const streakStart = '2023-10-29' as IsoDate;
      const streakEnd = '2023-11-11' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      // Oct 29 to Nov 11 inclusive = 14 days
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 14,
      });
    });

    it('should validate exact user data scenario - current streak active today', () => {
      // Arrange - Exact user data
      // movementStreak: {
      //   lastCompletedIsoDate: "2025-06-29"
      //   longestStreakEndIsoDate: "2023-10-29"
      //   longestStreakStartIsoDate: "2023-11-11"
      //   streakStartIsoDate: "2025-06-25"
      // }
      const currentStreakStart = '2025-06-25' as IsoDate;
      const currentStreakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(currentStreakStart, currentStreakEnd, todayIsoDate);

      // Assert
      expect(result.days).toBe(5);
      expect(result.dates).toEqual([currentStreakStart, currentStreakEnd]);
    });

    it('should validate exact user data scenario - current streak active yesterday', () => {
      // Arrange - Current streak but checking from tomorrow's perspective
      const currentStreakStart = '2025-06-25' as IsoDate;
      const currentStreakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-30' as IsoDate; // Tomorrow

      // Act
      const result = calculateStreak(currentStreakStart, currentStreakEnd, todayIsoDate);

      // Assert
      expect(result.days).toBe(5); // Should still count as active (yesterday)
      expect(result.dates).toEqual([currentStreakStart, currentStreakEnd]);
    });

    it('should validate exact user data scenario - current streak expired', () => {
      // Arrange - Current streak but checking from day after tomorrow
      const currentStreakStart = '2025-06-25' as IsoDate;
      const currentStreakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-07-01' as IsoDate; // Day after tomorrow

      // Act
      const result = calculateStreak(currentStreakStart, currentStreakEnd, todayIsoDate);

      // Assert
      expect(result.days).toBe(0); // Should be expired (more than 1 day ago)
    });
  });

  describe('edge cases', () => {
    it('should handle single day streak', () => {
      // Arrange
      const streakStart = '2025-06-29' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 1,
      });
    });

    it('should handle streak that ended exactly yesterday', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-28' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 4, // 2025-06-25 to 2025-06-28 inclusive = 4 days
      });
    });

    it('should handle streak that ended two days ago', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-27' as IsoDate;
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({days: 0});
    });
  });

  describe('without todayIsoDate parameter', () => {
    it('should calculate streak without today validation', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 5,
      });
    });

    it('should calculate historical streak without today validation', () => {
      // Arrange
      const streakStart = '2023-01-01' as IsoDate;
      const streakEnd = '2023-01-10' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 10, // Jan 1 to Jan 10 inclusive = 10 days
      });
    });
  });

  describe('date order handling', () => {
    it('should handle correctly ordered dates', () => {
      // Arrange
      const streakStart = '2025-06-25' as IsoDate;
      const streakEnd = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({
        dates: [streakStart, streakEnd],
        days: 5,
      });
    });

    it('should handle flipped dates by correcting the order', () => {
      // Arrange - Dates are intentionally flipped
      const streakStart = '2025-06-29' as IsoDate; // Actually the end
      const streakEnd = '2025-06-25' as IsoDate; // Actually the start

      // Act
      const result = calculateStreak(streakStart, streakEnd);

      // Assert
      expect(result).toEqual({
        dates: ['2025-06-25' as IsoDate, '2025-06-29' as IsoDate], // Corrected order
        days: 5,
      });
    });

    it('should handle flipped dates with today validation', () => {
      // Arrange - Flipped dates with today check
      const streakStart = '2025-06-29' as IsoDate; // Actually the end
      const streakEnd = '2025-06-25' as IsoDate; // Actually the start
      const todayIsoDate = '2025-06-29' as IsoDate;

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({
        dates: ['2025-06-25' as IsoDate, '2025-06-29' as IsoDate], // Corrected order
        days: 5,
      });
    });

    it('should handle flipped dates that are expired', () => {
      // Arrange - Flipped dates that should be expired
      const streakStart = '2025-06-27' as IsoDate; // Actually the end
      const streakEnd = '2025-06-25' as IsoDate; // Actually the start
      const todayIsoDate = '2025-06-29' as IsoDate; // 2 days after actual end

      // Act
      const result = calculateStreak(streakStart, streakEnd, todayIsoDate);

      // Assert
      expect(result).toEqual({days: 0}); // Should be expired
    });
  });

  describe('exact user data validation', () => {
    it('should handle the exact user movementStreak data correctly', () => {
      // Arrange - Exact data from user's movementStreak object
      // movementStreak: {
      //   lastCompletedIsoDate: "2025-06-29"
      //   longestStreakEndIsoDate: "2023-10-29"
      //   longestStreakStartIsoDate: "2023-11-11"
      //   streakStartIsoDate: "2025-06-25"
      // }

      // Test current streak
      const currentResult = calculateStreak(
        '2025-06-25' as IsoDate, // streakStartIsoDate
        '2025-06-29' as IsoDate, // lastCompletedIsoDate
        '2025-06-29' as IsoDate, // today
      );

      // Test longest streak (with flipped dates)
      const longestResult = calculateStreak(
        '2023-11-11' as IsoDate, // longestStreakStartIsoDate (actually the end)
        '2023-10-29' as IsoDate, // longestStreakEndIsoDate (actually the start)
      );

      // Assert
      expect(currentResult).toEqual({
        dates: ['2025-06-25' as IsoDate, '2025-06-29' as IsoDate],
        days: 5,
      });

      expect(longestResult).toEqual({
        dates: ['2023-10-29' as IsoDate, '2023-11-11' as IsoDate], // Corrected order
        days: 14,
      });
    });
  });
});
