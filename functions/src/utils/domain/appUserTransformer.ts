import {DEFAULT_TIME_ZONE} from '../../constants';
import {
  getAllUserParticipatingCompleteWorkouts,
  getMetadataHealthDataSummary,
} from '../../firestore';
import {
  type AppUser,
  type ClientAppUserDTO2,
  transformAppUserToBaseUser,
} from '../../types';
import {
  getIsoStringFromDate,
  transformAnyTimestampsToFirebaseAdminTimestamp,
} from '../primitives';
import {getWorkoutDurationInMs, sortWorkoutsByTimestampsDescending} from './workoutHelpers';
import {calculateStreak} from './streakCalculator';

// eslint-disable-next-line complexity -- allow for conditional spreading
export const getClientAppUserDTO2 = async (
  appUser: AppUser,
  todayDate = new Date(),
): Promise<ClientAppUserDTO2> => {
  const allUserWorkouts = await getAllUserParticipatingCompleteWorkouts(appUser.id);
  // Calculate basic metrics
  const totalNumberOfWorkouts = allUserWorkouts.length;
  const totalDurationOfWorkoutsInMs = allUserWorkouts.reduce(
    (acc, workout) => acc + getWorkoutDurationInMs(workout),
    0,
  );

  // Get the last workout
  const [lastWorkout] = allUserWorkouts.toSorted(sortWorkoutsByTimestampsDescending);

  // Calculate streaks
  const timeZone = appUser.timeZone ?? DEFAULT_TIME_ZONE;
  const todayIsoDate = getIsoStringFromDate(todayDate, timeZone);
  const currentStreak = calculateStreak(
    appUser.movementStreak?.streakStartIsoDate,
    appUser.movementStreak?.lastCompletedIsoDate,
    todayIsoDate,
  );
  const longestStreak = calculateStreak(
    appUser.movementStreak?.longestStreakStartIsoDate,
    appUser.movementStreak?.longestStreakEndIsoDate,
  );

  // Calculate health stats
  const summaryHealthData = await getMetadataHealthDataSummary(appUser.id);
  const totalStepsCount = summaryHealthData?.totalStepsCount ?? 0;
  const totalDistanceMeters = summaryHealthData?.totalDistanceMeters ?? 0;
  const maxStepsInOneDay = summaryHealthData?.maxStepsInOneDay ?? 0;
  const maxStepsInOneDayDateTime = summaryHealthData?.maxStepsInOneDayDateTime;

  // Assemble DTO
  return {
    totalNumberOfWorkouts,
    totalDurationOfWorkoutsInMs,
    currentStreakInDays: currentStreak.days,
    longestStreakInDays: longestStreak.days,
    totalDistanceMeters,
    totalStepsCount,
    maxStepsInOneDay,
    ...(currentStreak.dates ? {currentStreakDates: currentStreak.dates} : {}),
    ...(longestStreak.dates ? {longestStreakDates: longestStreak.dates} : {}),
    ...(maxStepsInOneDayDateTime ? {maxStepsInOneDayDateTime} : {}),
    ...(appUser.lastHealthStatsSync ? {lastLoginDateTime: appUser.lastHealthStatsSync} : {}),
    ...(lastWorkout?.startedDateTime ? {lastWorkoutDateTime: lastWorkout.startedDateTime} : {}),
    ...(lastWorkout?.workoutName ? {lastWorkoutName: lastWorkout.workoutName} : {}),
    ...transformAppUserToBaseUser(appUser),
  };
};

export const getAppUserWithAdminTimestamps = (
  appUser: Partial<AppUser> & Pick<AppUser, 'id'>,
): Partial<AppUser> & Pick<AppUser, 'id'> => {
  const modifiedAppUser = {...appUser};
  if (modifiedAppUser.healthDayStats) {
    modifiedAppUser.healthDayStats = modifiedAppUser.healthDayStats.map(
      transformAnyTimestampsToFirebaseAdminTimestamp,
    );
  }
  if (modifiedAppUser.weightSamples) {
    modifiedAppUser.weightSamples = modifiedAppUser.weightSamples.map(
      transformAnyTimestampsToFirebaseAdminTimestamp,
    );
  }

  return transformAnyTimestampsToFirebaseAdminTimestamp(modifiedAppUser);
};
