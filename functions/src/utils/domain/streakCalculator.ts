import {LOGGER} from '../../firebase';
import {DOMAIN_CONSTANTS, type IsoDate} from '../../types';
import {getIsoDateDaysBefore, getIsoDateDifference} from '../primitives';

// Helper function to calculate streak information
export const calculateStreak = (
  streakStart: IsoDate | undefined,
  streakEnd: IsoDate | undefined,
  todayIsoDate?: IsoDate,
): {dates?: [IsoDate, IsoDate]; days: number} => {
  if (!streakStart || !streakEnd) return {days: 0};

  // Determine the actual start and end dates (handle flipped dates)
  const rawDifference = getIsoDateDifference(streakEnd, streakStart, false);
  const actualStart = rawDifference >= 0 ? streakStart : streakEnd;
  const actualEnd = rawDifference >= 0 ? streakEnd : streakStart;

  const days = getIsoDateDifference(actualEnd, actualStart, true);

  // If a today date was given, if the last date is not today or yesterday, return 0 days
  const yesterdayIsoDate = todayIsoDate && getIsoDateDaysBefore(todayIsoDate, 1);
  if (
    todayIsoDate &&
    actualEnd !== todayIsoDate &&
    actualEnd !== yesterdayIsoDate &&
    yesterdayIsoDate
  ) {
    LOGGER.debug(
      `Last completed date ${actualEnd} is not today=${todayIsoDate} or yesterday=${yesterdayIsoDate}, returning 0 days, todayDateIso=${todayIsoDate}`,
    );
    return {days: 0};
  }

  // Ensure streak is long enough to be counted
  if (days < DOMAIN_CONSTANTS().STREAKS.MIN_MOVEMENT_STREAK_LENGTH) {
    return {days: 0};
  }

  return {dates: [actualStart, actualEnd], days};
};
