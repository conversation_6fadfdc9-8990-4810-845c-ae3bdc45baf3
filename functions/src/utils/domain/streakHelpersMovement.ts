import {LOGGER} from '../../firebase';
import {
  buildMovementStreakDocumentId,
  DOMAIN_CONSTANTS,
  type HealthStats,
  type IsoDate,
  type MovementStreakDocument,
  type MovementStreakValues,
  type StreakDocument,
  timestampToDate,
  type TimeZones,
  type UUIDString,
  type Workout,
} from '../../types';
import {
  arrayDifferenceMutable,
  filterTruthy,
  findLongestArrayIn2dArray,
  getIsoDateDaysBefore,
  getIsoDateDifference,
  getIsoStringFromDate,
  isEmptyArray,
  sortIsoDatesDesc,
} from '../primitives';
import {getGroupedHealthStatsByIsoDate} from './healthStatsHelpers';

export type MovementStreakOptions = {
  goals: {
    dailyGoalMileageInMeters: number;
    dailyGoalStepCount: number;
  };
  healthStats: HealthStats[];
  timeZone: TimeZones;
  userId: UUIDString;
  workouts: Workout[];
};

const getMovementStreakWhereWorkoutIsOn = (
  userId: UUIDString,
  workouts: Workout[],
  timeZone: TimeZones,
): MovementStreakValues[] =>
  workouts.map(workout => {
    const completedIsoDate = getIsoStringFromDate(
      timestampToDate(workout.startedDateTime),
      timeZone,
    );

    return {
      completedIsoDate,
      hasMetDistanceGoal: false,
      hasMetStepGoal: false,
      hasMetWorkoutGoal: true,
      userId,
    };
  });

const getMovementStreakWhereGoalsMet = (options: MovementStreakOptions): MovementStreakValues[] => {
  // Group health stats by day (all intervals of HealthStats are combined to one day)
  const groupedHealthStats = getGroupedHealthStatsByIsoDate(options.healthStats, options.timeZone);

  // Get movement streak data if their movement goal has been met
  return groupedHealthStats
    .map(stat => {
      // Return undefined if goal has not been met
      const hasMetStepGoal = (stat.stepsCount ?? 0) >= options.goals.dailyGoalStepCount;
      const hasMetDistanceGoal =
        (stat.distanceMeters ?? 0) >= options.goals.dailyGoalMileageInMeters;
      if (!hasMetStepGoal && !hasMetDistanceGoal) return;

      const completedIsoDate = getIsoStringFromDate(
        timestampToDate(stat.startDate),
        options.timeZone,
      );

      return {
        completedIsoDate,
        hasMetDistanceGoal,
        hasMetStepGoal,
        hasMetWorkoutGoal: false,
        userId: options.userId,
      };
    })
    .filter(filterTruthy);
};

// Helper function to get all possible streak dates, i.e. all workout dates and goal met dates de-duplicated and sorted
export const getUniqueSortedMovementStreakDates = (
  options: MovementStreakOptions,
): MovementStreakDocument[] => {
  const workoutMovements = getMovementStreakWhereWorkoutIsOn(
    options.userId,
    options.workouts,
    options.timeZone,
  );
  const goalMovements = getMovementStreakWhereGoalsMet(options);

  const mergedMovementStreak = [...workoutMovements, ...goalMovements].reduce((acc, movement) => {
    const {completedIsoDate, hasMetDistanceGoal, hasMetStepGoal, hasMetWorkoutGoal} = movement;

    if (!acc.has(completedIsoDate)) {
      acc.set(completedIsoDate, {
        completedIsoDate,
        hasMetDistanceGoal: false,
        hasMetStepGoal: false,
        hasMetWorkoutGoal: false,
        userId: options.userId,
        id: buildMovementStreakDocumentId(options.userId, completedIsoDate),
      });
    }

    const currentMovementStreak = acc.get(completedIsoDate)!;
    currentMovementStreak.hasMetDistanceGoal ||= hasMetDistanceGoal;
    currentMovementStreak.hasMetStepGoal ||= hasMetStepGoal;
    currentMovementStreak.hasMetWorkoutGoal ||= hasMetWorkoutGoal;

    return acc;
  }, new Map<string, MovementStreakDocument>());

  return [...mergedMovementStreak.values()].sort((a, b) =>
    sortIsoDatesDesc(a.completedIsoDate, b.completedIsoDate),
  );
};

// Helper function to find streak dates starting from a given reference date
const findMovementStreaksFromReference = (
  allMovementStreaks: StreakDocument[],
  referenceDate: IsoDate,
): StreakDocument[] => {
  const movementStreaks: StreakDocument[] = [];
  let currentStreakDate = referenceDate;

  // For each unique workout day, check if it is the reference date
  // and decrement the reference date by one day if it is to keep checking if streak
  // eslint-disable-next-line no-loops/no-loops -- for allowing breaking loop early
  for (const movementStreak of allMovementStreaks) {
    if (movementStreak.completedIsoDate === currentStreakDate) {
      movementStreaks.push(movementStreak);
      currentStreakDate = getIsoDateDaysBefore(currentStreakDate, 1);
    } else {
      break; // Gap detected, break the loop
    }
  }

  // Ensure there are the number of consecutive dates for a valid streak
  if (movementStreaks.length < DOMAIN_CONSTANTS().STREAKS.MIN_MOVEMENT_STREAK_LENGTH) {
    return []; // Not enough consecutive days to be considered a streak
  }

  return movementStreaks;
};

export const getAllStreakDates2D = (allMovementStreaks: StreakDocument[]) => {
  const allStreaks: StreakDocument[][] = [];
  const remainingStreaks = [...allMovementStreaks];

  // eslint-disable-next-line no-loops/no-loops -- for simplicity
  while (remainingStreaks.length > 0) {
    const referenceMovementStreak = remainingStreaks[0]!;
    const movementStreaks = findMovementStreaksFromReference(
      remainingStreaks,
      referenceMovementStreak.completedIsoDate,
    );
    if (isEmptyArray(movementStreaks)) {
      remainingStreaks.shift(); // No streak found, move to the next date
    } else {
      // Streak found, add streak array to 2D array
      allStreaks.push(movementStreaks);

      // Remove those newly added streak dates from allDates
      arrayDifferenceMutable(remainingStreaks, movementStreaks);
    }
  }

  return allStreaks;
};

export const findStreakFromReferenceIsoDate = (
  streaks: IsoDate[][],
  referenceDate: IsoDate,
): IsoDate[] => {
  const previousDay = getIsoDateDaysBefore(referenceDate, 1);

  // First return the first streak that has the reference date in the streak range
  //   If no streak found, then check if the previous day has a streak
  //   If no streak found, return empty array
  return (
    streaks.find(streak => streak.includes(referenceDate)) ??
    streaks.find(streak => streak.includes(previousDay)) ??
    []
  );
};

const findStreakFromReferenceDate = <T extends StreakDocument>(
  streaks: T[][],
  referenceDate: IsoDate,
): T[] => {
  const previousDay = getIsoDateDaysBefore(referenceDate, 1);

  // First return the first streak that has the reference date in the streak range
  //   If no streak found, then check if the previous day has a streak
  //   If no streak found, return empty array
  return (
    streaks.find(streak => streak.some(s => s.completedIsoDate === referenceDate)) ??
    streaks.find(streak => streak.some(s => s.completedIsoDate === previousDay)) ??
    []
  );
};

const findLongestStreak = findLongestArrayIn2dArray<StreakDocument>;

export const calculateMovementStreakMetadataFromDocs = (
  streakDocs: StreakDocument[],
  referenceDate: IsoDate,
) => {
  if (isEmptyArray(streakDocs)) return;

  // Sort streak documents by date (newest first)
  const sortedDocs = [...streakDocs].sort((a, b) =>
    getIsoDateDifference(b.completedIsoDate, a.completedIsoDate),
  );
  const streakDocs2D = getAllStreakDates2D(sortedDocs);

  const currentStreak = findStreakFromReferenceDate(streakDocs2D, referenceDate);
  const longestStreak = findLongestStreak(streakDocs2D);

  // Get the last completed date (most recent)
  const lastCompletedDate = sortedDocs.at(0);
  const streakStartDate = currentStreak.at(-1) ?? lastCompletedDate;
  const longestStreakStart = longestStreak.at(-1) ?? streakStartDate;
  const longestStreakEnd = longestStreak.at(0) ?? longestStreakStart;

  if (!lastCompletedDate || !streakStartDate || !longestStreakStart || !longestStreakEnd) {
    LOGGER.error(
      `There was an error calculating movement streak metadata from ${streakDocs.length} documents`,
    );
    return;
  }

  return {
    streakStartIsoDate: streakStartDate.completedIsoDate,
    lastCompletedIsoDate: lastCompletedDate.completedIsoDate,
    longestStreakStartIsoDate: longestStreakStart.completedIsoDate,
    longestStreakEndIsoDate: longestStreakEnd.completedIsoDate,
  };
};
