import {openURL} from 'expo-linking';
import {useQuery} from '@tanstack/react-query';
import {getLinkPreview} from 'link-preview-js';
import {useImage} from 'expo-image';
import {Box, LoadingIndicator, Text, TouchableHighlight} from '@base-components';
import {useDimensions} from '@hooks';
import {getSensibleDomain, memoComponent, useAppTheme} from '@utils';
import {Images, placeholderPostImage} from '@assets';
import {MS_1_HOUR} from '@constants';

type BaseLinkPreviewResponse = {
  contentType: string;
  favicons: string[];
  url: string;
};

type HtmlLinkPreviewResponse = BaseLinkPreviewResponse & {
  charset?: string;
  description?: string;
  images?: string[];
  mediaType?: string;
  siteName?: string;
  title?: string;
  videos?: string[];
};

type MediaLinkPreviewResponse = BaseLinkPreviewResponse & {
  mediaType: 'image' | 'audio' | 'video' | 'application';
};

type LinkPreviewResponse = HtmlLinkPreviewResponse | MediaLinkPreviewResponse;

const LINK_PREVIEW_IMAGE_HEIGHT = 92;
const CONTAINER_PADDING = 32; // Screen width padding to calculate container width
const MAX_IMAGE_WIDTH_RATIO = 5 / 12; // Maximum image width as fraction of container (5/12)
const MIN_IMAGE_WIDTH_RATIO = 0.15; // Minimum image width as fraction of container (15%)
const ABSOLUTE_MIN_IMAGE_WIDTH = 80; // Absolute minimum image width in pixels

const MEDIA_TYPE_LABELS = {
  image: 'Image',
  audio: 'Audio',
  video: 'Video',
  application: 'File',
} as const;

const isHtmlLinkPreview = (response: LinkPreviewResponse): response is HtmlLinkPreviewResponse =>
  'title' in response || 'siteName' in response || 'description' in response || 'images' in response;

const useLinkPreview = (url: string) => {
  const {data, isPending} = useQuery<LinkPreviewResponse>({
    queryKey: ['linkPreview', url],
    queryFn: () => getLinkPreview(url) as Promise<LinkPreviewResponse>,
    enabled: !!url,
    staleTime: MS_1_HOUR,
  });

  const {width: screenWidth} = useDimensions();
  const domain = getSensibleDomain(url);

  // Get image dimensions for dynamic aspect ratio sizing
  const imageUrl = data && isHtmlLinkPreview(data) ? data.images?.[0] : undefined;
  const image = useImage(imageUrl || placeholderPostImage);
  const aspectRatio = image ? image.width / image.height : 1;

  // Include image loading state in isPending
  const isImageLoading = imageUrl && !image;
  const isOverallPending = isPending || isImageLoading;

  // Dynamic sizing based on aspect ratio with configurable ratios
  const getImageDimensions = () => {
    const containerWidth = screenWidth - CONTAINER_PADDING;
    const maxImageWidth = Math.floor(containerWidth * MAX_IMAGE_WIDTH_RATIO);
    // Calculate ideal width based on aspect ratio and configured height
    const idealWidth = Math.round(LINK_PREVIEW_IMAGE_HEIGHT * aspectRatio);
    const finalWidth = Math.min(idealWidth, maxImageWidth);

    // Ensure minimum width for very narrow images
    const minWidth = Math.min(
      ABSOLUTE_MIN_IMAGE_WIDTH,
      Math.floor(containerWidth * MIN_IMAGE_WIDTH_RATIO),
    );
    const constrainedWidth = Math.max(finalWidth, minWidth);

    return {width: constrainedWidth, minWidth: constrainedWidth};
  };

  const imageDimensions = getImageDimensions();

  const htmlData = data && isHtmlLinkPreview(data) ? data : null;
  const mediaData = data && !isHtmlLinkPreview(data) ? data : null;

  // Get friendly media type name
  const getFriendlyMediaType = () => {
    if (!mediaData?.mediaType) return 'Content';

    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- in case unhandled media types are added
    return MEDIA_TYPE_LABELS[mediaData.mediaType] || 'Content';
  };

  return {
    data,
    isPending: isOverallPending,
    domain,
    imageDimensions,
    isHtmlPreview: !!htmlData,
    isMediaPreview: !!mediaData,
    imageUrl: htmlData?.images?.[0],
    title: htmlData?.title,
    friendlyMediaType: getFriendlyMediaType(),
  };
};

type LinkPreviewProps = {
  url: string;
};

export const LinkPreview: React.FC<LinkPreviewProps> = memoComponent(({url}) => {
  const {
    domain,
    friendlyMediaType,
    imageDimensions,
    imageUrl,
    isHtmlPreview,
    isMediaPreview,
    isPending,
    title,
  } = useLinkPreview(url);
  const theme = useAppTheme();

  return (
    <TouchableHighlight onPress={() => void openURL(url)}>
      <Box style={{
        borderWidth: 1,
        borderColor: theme.colors.buttonBorderColor,
        borderRadius: 10,
        paddingHorizontal: 4,
        paddingVertical: 8,
        flexDirection: 'row',
        justifyContent: 'center',
      }}
      >
        {isPending && <LoadingIndicator />}
        {!isPending && isHtmlPreview && (
          <>
            <Box
              style={{
                borderRadius: 10,
                overflow: 'hidden',
                minWidth: imageDimensions.minWidth,
                justifyContent: 'center',
                alignItems: 'center',
                height: LINK_PREVIEW_IMAGE_HEIGHT,
              }}
            >
              <Images.placeholderPostImage
                contentFit='contain'
                overrideSource={imageUrl}
                style={{
                  width: imageDimensions.width,
                  height: LINK_PREVIEW_IMAGE_HEIGHT,
                  borderRadius: 10,
                }}
              />
            </Box>
            <Box flex={1} justifyContent='space-between' px={1}>
              <Text
                ellipsizeMode='tail'
                numberOfLines={3}
                style={{
                  color: theme.colors.linkColor,
                  flexWrap: 'wrap',
                  lineHeight: 20,
                  maxHeight: 60, // 3 lines * 20 line height
                  overflow: 'hidden',
                }}
                variant='labelMedium'
              >
                {title || url}
              </Text>
              <Text variant='bodySmall'>{domain}</Text>
            </Box>
          </>
        )}
        {!isPending && isMediaPreview && (
          <Box flex={1} justifyContent='center' p={1}>
            <Text style={{color: theme.colors.linkColor}} variant='labelLarge'>
              {friendlyMediaType} from {domain}
            </Text>
          </Box>
        )}
      </Box>
    </TouchableHighlight>
  );
});
