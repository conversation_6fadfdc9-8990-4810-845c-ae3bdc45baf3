import {LOGGER} from '../logger';

export const getSensibleDomain = (url: string): string => {
  try {
    const {hostname} = new URL(url);
    const parts = hostname.split('.');
    const numParts = parts.length;

    // Single part (e.g., "localhost")
    if (numParts <= 1) {
      return hostname;
    }

    // Two parts (e.g., "example.com")
    if (numParts === 2) {
      return hostname;
    }

    // Three or more parts - need to determine what makes sense
    const lastPart = parts[numParts - 1];
    const secondLastPart = parts[numParts - 2];

    // Handle common multi-part TLDs (co.uk, com.au, etc.)
    const multiPartTlds = new Set([
      'co.uk',
      'com.au',
      'co.jp',
      'com.br',
      'co.za',
      'com.mx',
      'co.in',
      'com.sg',
      'co.nz',
      'com.ar',
      'co.kr',
    ]);

    const possibleTld = `${secondLastPart}.${lastPart}`;
    if (multiPartTlds.has(possibleTld)) {
      // For multi-part TLDs, take domain + multi-part TLD
      return parts.slice(numParts - 3).join('.');
    }

    // Remove "www" subdomain specifically
    if (parts[0] === 'www') {
      return parts.slice(1).join('.');
    }

    // For service domains like "s3.amazonaws.com", keep all parts
    // This preserves meaningful service identifiers
    if (numParts === 3) {
      return hostname;
    }

    // For 4+ parts, remove the first subdomain unless it's meaningful
    // Keep service-like subdomains (s3, api, cdn, etc.)
    const meaningfulSubdomains = new Set([
      's3', 'api', 'cdn', 'mail', 'ftp', 'admin', 'app', 'dev', 'staging',
    ]);

    if (meaningfulSubdomains.has(parts[0]!)) {
      return hostname;
    }

    // Default: remove first subdomain for 4+ parts
    return parts.slice(1).join('.');
  } catch (error) {
    LOGGER.error(`Error parsing URL "${url}":`, error);
    return '';
  }
};
