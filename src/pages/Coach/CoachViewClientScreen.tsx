import {DumbbellIcon, DurationIcon, Images, StreakIcon} from '@assets';
import {Box, ContextMenuDotsVertical, LoaderWrapper, Text} from '@base-components';
import {
  ClientDataWarningSticky,
  CoachStatBox,
  HomeCalendarWrapper,
  ScreenContent,
  ScreenHeader,
  SummaryNumbersIndividual,
} from '@components';
import {ScreenWrapper, useAppUserById, useClientContextActions} from '@contexts';
import {useClientSummary} from '@data-hooks';
import {type CoachViewClientScreenProps} from '@navigation';
import type {AppUser} from '@types';
import {formatFullTimestamp, formatMillisecondsToHours, memoComponent, useAppTheme} from '@utils';

const ClientContent: React.FC<{client: AppUser | undefined}> = ({client}) => {
  const {data: summaryData, isPending} = useClientSummary(client?.id);
  const theme = useAppTheme();

  return (
    <>
      <Box flexDirection='row'>
        <Box pr={2}>
          <Images.defaultProfile
            overrideSource={client?.profilePicture}
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              marginHorizontal: 'auto',
            }}
          />
        </Box>
        <Box>
          <Text flexDirection='row' variant='labelLarge'>
            {'Last Sync: '}
            <Text>{formatFullTimestamp(summaryData?.lastLoginDateTime)}</Text>
          </Text>
          {client?.email && (
            <Text flexDirection='row' variant='labelLarge'>
              {'Email: '}
              <Text>{client.email}</Text>
            </Text>
          )}
          {client?.phoneNumber && (
            <Text flexDirection='row' variant='labelLarge'>
              {'Phone: '}
              <Text>{client.phoneNumber}</Text>
            </Text>
          )}
        </Box>
      </Box>

      <Text py={1} variant='headlineMedium'>
        Workout Summary
      </Text>

      <LoaderWrapper isLoading={!summaryData || isPending} minHeight={336}>
        {summaryData && (
          <>
            <CoachStatBox
              icon={<DumbbellIcon fill={theme.colors.dodgerBlue} />}
              label='Total Workouts'
              value={`${summaryData.totalNumberOfWorkouts}`}
            />

            <CoachStatBox
              icon={<DurationIcon fill={theme.colors.dodgerBlue} />}
              label='Duration (hours)'
              value={`${formatMillisecondsToHours(summaryData.totalDurationOfWorkoutsInMs)}`}
            />

            <CoachStatBox
              icon={<StreakIcon fill={theme.colors.dodgerBlue} />}
              label='Current Movement Streak'
              value={`${summaryData.currentStreakInDays}`}
            />

            <CoachStatBox
              icon={<StreakIcon fill={theme.colors.dodgerBlue} />}
              label='Longest Movement Streak'
              value={`${summaryData.longestStreakInDays}`}
            />
          </>
        )}
      </LoaderWrapper>

      <Text mt={2} py={1} variant='headlineMedium'>
        Calendar
      </Text>

      <LoaderWrapper hasSurface isLoading={!client} minHeight={340}>
        <Box mx={-2}>
          <HomeCalendarWrapper appUser={client} />
        </Box>
      </LoaderWrapper>

      <Text mt={2} py={1} variant='headlineMedium'>
        Summary Numbers
      </Text>

      <LoaderWrapper hasSurface isLoading={!client} minHeight={288}>
        {client && <SummaryNumbersIndividual appUser={client} />}
      </LoaderWrapper>

      <Box py={2} />
    </>
  );
};

const ClientContentMemo = memoComponent(ClientContent);

export const CoachViewClientScreen: React.FC<CoachViewClientScreenProps> = ({route}) => {
  const appUserId = route.params.clientId;
  const client = useAppUserById(appUserId);
  const headerPrefix = client ? 'Client' : 'View Client';
  const headerSuffix = client ? `: ${client.firstName} ${client.lastName}` : '';
  const contextActions = useClientContextActions(client);

  return (
    <ScreenWrapper>
      <ScreenHeader
        right={<ContextMenuDotsVertical actions={contextActions} />}
        title={`${headerPrefix}${headerSuffix}`}
      />
      <ClientDataWarningSticky />

      <ScreenContent>
        <ClientContentMemo client={client} />
      </ScreenContent>
    </ScreenWrapper>
  );
};
